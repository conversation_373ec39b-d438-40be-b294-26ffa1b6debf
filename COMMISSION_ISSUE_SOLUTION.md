# 🔧 Commission Issue Solution - <PERSON><PERSON><PERSON><PERSON> pháp cho vấn đề Mike

## 📋 Tóm tắt vấn đề:
- **<PERSON> thực hiện chỉ meme trade** nhưng **cả contract và meme commission đều tăng**
- **Daniel phát hiện**: Khi chỉ trade meme, chỉ nên có meme cashback tăng, không phải cả hai

## 🎯 Nguyên nhân đã xác định:

### 1. **Infinite Commission Task chạy quá thường xuyên**
```yaml
# config.yaml - TRƯỚC (SAI)
- id: "infinite_agent_commission"
  cron: "0 0 * * * *"  # Chạy MỖI GIỜ! ❌

# config.yaml - SAU (ĐÚNG) 
- id: "infinite_agent_commission"
  cron: "0 5 0 * * *"  # Chạy mỗi ngày lúc 00:05 ✅
```

### 2. **Infinite Commission tính toán cả meme và contract cùng lúc**
Mỗi khi task chạy, nó recalculate **cả meme và contract commission** cho tất cả infinite agents, bất kể trade type.

## 🛠️ <PERSON><PERSON><PERSON> thay đổi đã thực hiện:

### 1. **Fixed Cron Schedule** ✅
- Thay đổi từ mỗi giờ thành mỗi ngày
- Giảm frequency để tránh recalculation không cần thiết

### 2. **Enhanced Logging** ✅
- Thêm detailed logging trong `hyperliquid_transaction_task.go`
- Thêm detailed logging trong `affiliate_service.go`  
- Thêm detailed logging trong `infinite_agent_commission_task.go`

### 3. **Debug Tools** ✅
- Tạo `scripts/debug_commission.sql` - SQL queries để debug
- Tạo `scripts/monitor_commission_issue.sh` - Script monitor real-time
- Tạo `debug_commission_issue.md` - Detailed analysis

## 🔍 Cách sử dụng debug tools:

### 1. **SQL Debug Queries**
```bash
# Thay REPLACE_WITH_MIKE_USER_ID bằng user ID thực của Mike
psql -f scripts/debug_commission.sql
```

### 2. **Real-time Monitoring**
```bash
# Monitor commission activities cho Mike
MIKE_USER_ID="uuid-của-mike" ./scripts/monitor_commission_issue.sh
```

### 3. **Log Analysis**
```bash
# Tìm logs liên quan đến commission
grep -E "(Processing.*trade|calculate.*commission|infinite agent)" logs/app.log | tail -20
```

## 📊 Expected Behavior sau khi fix:

### ✅ **Khi Mike trade MEME:**
1. NATS event từ `agency.affiliate.xbit_tx`
2. `AffiliateService.ProcessAffiliateTransaction()` xử lý
3. **Chỉ meme commission tăng**
4. Infinite commission task chạy 1 lần/ngày (không ảnh hưởng real-time)

### ✅ **Khi Mike trade CONTRACT:**
1. NATS event từ `dex_hyperliquid_transaction`
2. `ContractCommissionService.ProcessContractCommission()` xử lý
3. **Chỉ contract commission tăng**
4. Infinite commission task chạy 1 lần/ngày (không ảnh hưởng real-time)

## 🚀 Deployment Steps:

### 1. **Immediate Fix (Hot Fix)**
```bash
# Restart scheduler service để apply cron change
systemctl restart xbit-agent-scheduler
```

### 2. **Verification**
```bash
# Check cron job đã update chưa
grep -A 5 "infinite_agent_commission" config.yaml

# Monitor logs để confirm
tail -f logs/app.log | grep "infinite agent commission"
```

### 3. **Testing với Mike**
1. Yêu cầu Mike thực hiện **1 meme trade**
2. Monitor bằng script: `MIKE_USER_ID="..." ./scripts/monitor_commission_issue.sh`
3. Verify chỉ meme commission tăng

## 📈 Monitoring & Alerts:

### **Red Flags to watch:**
- Infinite commission task chạy > 1 lần/giờ
- Cả meme và contract commission tăng khi chỉ có 1 loại trade
- Log messages về "negative net fee"

### **Success Indicators:**
- Infinite commission task chỉ chạy 1 lần/ngày lúc 00:05
- Commission type tương ứng với trade type
- Logs show correct `processing_source` identification

## 🔄 Rollback Plan:

Nếu có vấn đề, có thể rollback:
```yaml
# Temporary rollback to hourly (if needed)
- id: "infinite_agent_commission"
  cron: "0 0 * * * *"  # Back to hourly
```

## 📞 Next Steps:

1. **Deploy fix** và restart scheduler
2. **Test với Mike** để confirm
3. **Monitor 24h** để ensure stability
4. **Document lessons learned** cho team

## 🎯 Root Cause Summary:

**Vấn đề KHÔNG phải do NATS monitoring bị lỗi**, mà do:
- Infinite commission task chạy quá thường xuyên (mỗi giờ thay vì mỗi ngày)
- Task này recalculate cả meme và contract commission mỗi lần chạy
- Tạo illusion rằng cả hai loại commission đều tăng khi chỉ có 1 loại trade

**Fix chính**: Thay đổi cron schedule từ hourly thành daily.
