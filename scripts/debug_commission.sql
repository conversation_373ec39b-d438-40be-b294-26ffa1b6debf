-- Debug Commission Issue - SQL queries để kiểm tra vấn đề

-- 1. <PERSON><PERSON>m tra user Mike có bao nhiêu meme trades
SELECT 
    COUNT(*) as meme_trade_count,
    SUM(platform_fee) as total_meme_fees,
    MAX(created_at) as last_meme_trade
FROM affiliate_transactions 
WHERE user_id = 'REPLACE_WITH_MIKE_USER_ID';

-- 2. <PERSON><PERSON>m tra user Mike có bao nhiêu contract trades  
SELECT 
    COUNT(*) as contract_trade_count,
    SUM(COALESCE(build_fee, 0)) as total_contract_fees,
    MAX(created_at) as last_contract_trade
FROM hyper_liquid_transactions 
WHERE user_id = 'REPLACE_WITH_MIKE_USER_ID';

-- 3. <PERSON><PERSON><PERSON> tra Mike có referrer không và referrer có infinite agent config không
SELECT 
    ur.user_id,
    ur.referrer_id,
    iac.id as infinite_agent_config_id,
    iac.commission_rate_n,
    iac.status,
    iac.last_calculation_at,
    iac.total_net_fee_usd,
    iac.meme_net_fee_usd,
    iac.contract_net_fee_usd
FROM user_referrals ur
LEFT JOIN infinite_agent_configs iac ON ur.referrer_id = iac.user_id
WHERE ur.user_id = 'REPLACE_WITH_MIKE_USER_ID';

-- 4. Kiểm tra meme commission ledger cho Mike
SELECT 
    mcl.*,
    u.email as recipient_email
FROM meme_commission_ledger mcl
LEFT JOIN users u ON mcl.recipient_user_id = u.id
WHERE mcl.source_user_id = 'REPLACE_WITH_MIKE_USER_ID'
ORDER BY mcl.created_at DESC
LIMIT 10;

-- 5. Kiểm tra contract commission ledger cho Mike
SELECT 
    cl.*,
    u.email as recipient_email  
FROM commission_ledger cl
LEFT JOIN users u ON cl.recipient_user_id = u.id
WHERE cl.source_user_id = 'REPLACE_WITH_MIKE_USER_ID'
ORDER BY cl.created_at DESC
LIMIT 10;

-- 6. Kiểm tra activity cashback cho Mike
SELECT 
    ac.*,
    at.platform_fee,
    at.quote_amount,
    at.base_symbol,
    at.quote_symbol
FROM activity_cashback ac
INNER JOIN affiliate_transactions at ON ac.affiliate_transaction_id = at.id
WHERE at.user_id = 'REPLACE_WITH_MIKE_USER_ID'
ORDER BY ac.created_at DESC
LIMIT 10;

-- 7. Kiểm tra infinite agent commission updates gần đây
SELECT 
    iac.user_id,
    u.email,
    iac.total_net_fee_usd,
    iac.meme_net_fee_usd,
    iac.contract_net_fee_usd,
    iac.last_calculation_at,
    iac.updated_at
FROM infinite_agent_configs iac
LEFT JOIN users u ON iac.user_id = u.id
WHERE iac.status = 'ACTIVE'
  AND iac.updated_at >= NOW() - INTERVAL '24 HOURS'
ORDER BY iac.updated_at DESC;

-- 8. Tìm tất cả users trong referral tree của Mike
WITH RECURSIVE referral_tree AS (
    -- Base case: Mike
    SELECT user_id, referrer_id, 1 as level
    FROM user_referrals 
    WHERE user_id = 'REPLACE_WITH_MIKE_USER_ID'
    
    UNION ALL
    
    -- Recursive case: tìm upline
    SELECT ur.user_id, ur.referrer_id, rt.level + 1
    FROM user_referrals ur
    INNER JOIN referral_tree rt ON ur.user_id = rt.referrer_id
    WHERE rt.level < 10  -- Giới hạn 10 levels
)
SELECT 
    rt.*,
    u.email,
    iac.id as infinite_agent_config_id,
    iac.commission_rate_n
FROM referral_tree rt
LEFT JOIN users u ON rt.user_id = u.id
LEFT JOIN infinite_agent_configs iac ON rt.user_id = iac.user_id AND iac.status = 'ACTIVE'
ORDER BY rt.level;

-- 9. Kiểm tra recent trades và commission calculations
SELECT 
    'MEME' as trade_type,
    at.user_id,
    at.created_at as trade_time,
    at.platform_fee as fee_amount,
    COUNT(mcl.id) as commission_records
FROM affiliate_transactions at
LEFT JOIN meme_commission_ledger mcl ON at.user_id = mcl.source_user_id 
    AND DATE(at.created_at) = DATE(mcl.created_at)
WHERE at.created_at >= NOW() - INTERVAL '24 HOURS'
GROUP BY at.id, at.user_id, at.created_at, at.platform_fee

UNION ALL

SELECT 
    'CONTRACT' as trade_type,
    hlt.user_id,
    hlt.created_at as trade_time,
    hlt.build_fee as fee_amount,
    COUNT(cl.id) as commission_records
FROM hyper_liquid_transactions hlt
LEFT JOIN commission_ledger cl ON hlt.user_id = cl.source_user_id 
    AND DATE(hlt.created_at) = DATE(cl.created_at)
WHERE hlt.created_at >= NOW() - INTERVAL '24 HOURS'
  AND hlt.status = 'filled'
GROUP BY hlt.id, hlt.user_id, hlt.created_at, hlt.build_fee
ORDER BY trade_time DESC;

-- 10. Kiểm tra cron job execution logs (nếu có bảng log)
-- SELECT * FROM cron_job_logs 
-- WHERE job_name = 'infinite_agent_commission' 
--   AND executed_at >= NOW() - INTERVAL '24 HOURS'
-- ORDER BY executed_at DESC;
