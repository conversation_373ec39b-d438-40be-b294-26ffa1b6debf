#!/bin/bash

# Monitor Commission Issue Script
# Sử dụng để debug vấn đề Mike gặp phải

echo "🔍 Commission Issue Monitor Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
LOG_FILE="${LOG_FILE:-logs/app.log}"
MIKE_USER_ID="${MIKE_USER_ID:-}"
MONITOR_DURATION="${MONITOR_DURATION:-300}" # 5 minutes default

if [ -z "$MIKE_USER_ID" ]; then
    echo -e "${RED}❌ Error: MIKE_USER_ID environment variable is required${NC}"
    echo "Usage: MIKE_USER_ID=uuid ./scripts/monitor_commission_issue.sh"
    exit 1
fi

echo -e "${BLUE}📊 Monitoring user: ${MIKE_USER_ID}${NC}"
echo -e "${BLUE}📁 Log file: ${LOG_FILE}${NC}"
echo -e "${BLUE}⏱️  Duration: ${MONITOR_DURATION} seconds${NC}"
echo ""

# Function to check current commission state
check_commission_state() {
    echo -e "${YELLOW}📈 Current Commission State:${NC}"
    
    # Check meme trades
    MEME_COUNT=$(psql -t -c "SELECT COUNT(*) FROM affiliate_transactions WHERE user_id = '$MIKE_USER_ID';" 2>/dev/null || echo "0")
    echo -e "  Meme trades: ${GREEN}${MEME_COUNT}${NC}"
    
    # Check contract trades  
    CONTRACT_COUNT=$(psql -t -c "SELECT COUNT(*) FROM hyper_liquid_transactions WHERE user_id = '$MIKE_USER_ID';" 2>/dev/null || echo "0")
    echo -e "  Contract trades: ${GREEN}${CONTRACT_COUNT}${NC}"
    
    # Check meme commissions
    MEME_COMM_COUNT=$(psql -t -c "SELECT COUNT(*) FROM meme_commission_ledger WHERE source_user_id = '$MIKE_USER_ID';" 2>/dev/null || echo "0")
    echo -e "  Meme commissions: ${GREEN}${MEME_COMM_COUNT}${NC}"
    
    # Check contract commissions
    CONTRACT_COMM_COUNT=$(psql -t -c "SELECT COUNT(*) FROM commission_ledger WHERE source_user_id = '$MIKE_USER_ID';" 2>/dev/null || echo "0")
    echo -e "  Contract commissions: ${GREEN}${CONTRACT_COMM_COUNT}${NC}"
    
    echo ""
}

# Function to monitor logs in real-time
monitor_logs() {
    echo -e "${YELLOW}📝 Monitoring logs for commission activities...${NC}"
    echo -e "${BLUE}Press Ctrl+C to stop monitoring${NC}"
    echo ""
    
    # Monitor specific patterns
    tail -f "$LOG_FILE" | grep --line-buffered -E "(${MIKE_USER_ID}|Processing.*trade.*MEME|Processing.*trade.*PERPETUAL|calculate contract commission|infinite agent commission|Starting.*trade.*processing)" | while read line; do
        timestamp=$(echo "$line" | grep -o '[0-9]\{4\}-[0-9]\{2\}-[0-9]\{2\}T[0-9]\{2\}:[0-9]\{2\}:[0-9]\{2\}')
        
        if echo "$line" | grep -q "MEME"; then
            echo -e "${GREEN}[${timestamp}] 🟢 MEME: ${line}${NC}"
        elif echo "$line" | grep -q "PERPETUAL\|contract commission"; then
            echo -e "${RED}[${timestamp}] 🔴 CONTRACT: ${line}${NC}"
        elif echo "$line" | grep -q "infinite agent commission"; then
            echo -e "${YELLOW}[${timestamp}] 🟡 INFINITE: ${line}${NC}"
        else
            echo -e "${BLUE}[${timestamp}] 🔵 OTHER: ${line}${NC}"
        fi
    done
}

# Function to analyze recent logs
analyze_recent_logs() {
    echo -e "${YELLOW}📊 Analyzing recent logs (last 1 hour)...${NC}"
    
    # Get recent logs
    RECENT_LOGS=$(tail -n 10000 "$LOG_FILE" | grep -E "(${MIKE_USER_ID}|Processing.*trade|calculate.*commission|infinite agent)" | tail -n 50)
    
    if [ -z "$RECENT_LOGS" ]; then
        echo -e "${RED}❌ No recent activity found for user ${MIKE_USER_ID}${NC}"
        return
    fi
    
    echo -e "${GREEN}✅ Found recent activity:${NC}"
    echo "$RECENT_LOGS" | while read line; do
        if echo "$line" | grep -q "MEME"; then
            echo -e "${GREEN}  🟢 ${line}${NC}"
        elif echo "$line" | grep -q "PERPETUAL\|contract"; then
            echo -e "${RED}  🔴 ${line}${NC}"
        elif echo "$line" | grep -q "infinite"; then
            echo -e "${YELLOW}  🟡 ${line}${NC}"
        fi
    done
    echo ""
}

# Function to check cron job status
check_cron_status() {
    echo -e "${YELLOW}⏰ Checking cron job status...${NC}"
    
    # Check if infinite commission task is running frequently
    CRON_PATTERN="infinite.*agent.*commission"
    RECENT_CRON=$(tail -n 1000 "$LOG_FILE" | grep -c "$CRON_PATTERN" || echo "0")
    
    echo -e "  Recent infinite commission executions: ${GREEN}${RECENT_CRON}${NC}"
    
    if [ "$RECENT_CRON" -gt 10 ]; then
        echo -e "${RED}  ⚠️  WARNING: Infinite commission task running very frequently!${NC}"
        echo -e "${RED}     This might be the cause of the issue.${NC}"
    fi
    echo ""
}

# Function to simulate trade and monitor
simulate_trade_monitoring() {
    echo -e "${YELLOW}🧪 Trade Simulation Monitor${NC}"
    echo -e "${BLUE}This will monitor for the next ${MONITOR_DURATION} seconds${NC}"
    echo -e "${BLUE}Please perform a trade now and observe the logs...${NC}"
    echo ""
    
    # Start monitoring in background
    timeout "$MONITOR_DURATION" monitor_logs &
    MONITOR_PID=$!
    
    # Check state every 30 seconds
    for i in $(seq 0 30 "$MONITOR_DURATION"); do
        sleep 30
        echo -e "\n${YELLOW}📊 State check at ${i}s:${NC}"
        check_commission_state
    done
    
    # Kill monitor if still running
    kill $MONITOR_PID 2>/dev/null || true
}

# Main menu
show_menu() {
    echo -e "${BLUE}Choose monitoring option:${NC}"
    echo "1. Check current commission state"
    echo "2. Analyze recent logs"
    echo "3. Check cron job status"
    echo "4. Monitor logs in real-time"
    echo "5. Simulate trade monitoring"
    echo "6. Run all checks"
    echo "0. Exit"
    echo ""
    read -p "Enter your choice [0-6]: " choice
}

# Main execution
main() {
    while true; do
        show_menu
        case $choice in
            1)
                check_commission_state
                ;;
            2)
                analyze_recent_logs
                ;;
            3)
                check_cron_status
                ;;
            4)
                monitor_logs
                ;;
            5)
                simulate_trade_monitoring
                ;;
            6)
                check_commission_state
                analyze_recent_logs
                check_cron_status
                ;;
            0)
                echo -e "${GREEN}👋 Goodbye!${NC}"
                exit 0
                ;;
            *)
                echo -e "${RED}❌ Invalid option. Please try again.${NC}"
                ;;
        esac
        echo ""
        read -p "Press Enter to continue..."
        clear
    done
}

# Check if log file exists
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}❌ Error: Log file ${LOG_FILE} not found${NC}"
    echo "Please check the LOG_FILE path or start the application first."
    exit 1
fi

# Run main function
main
