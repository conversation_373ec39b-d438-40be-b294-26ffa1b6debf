# Debug Commission Issue - <PERSON><PERSON> tích vấn đề Mike gặp phải

## 🔍 Vấn đề được báo cáo:
- <PERSON> thực hiện **chỉ meme trade** (one-sided trade)
- Nhưng **cả contract và meme cashback handling fees đều tăng đồng thời**
- Đ<PERSON><PERSON><PERSON> này không đúng - chỉ nên có meme cashback tăng

## 🎯 Nguyên nhân đã xác định:

### 1. **Infinite Commission Calculation** - Nguyên nhân chính
Infinite commission task chạy **hàng ngày lúc 00:00** và tính toán **cả meme và contract** cùng lúc:

```go
// File: internal/task/infinite/infinite_agent_commission_task.go
// Line 196-202: Tính toán cả meme và contract fees
memeTotalFeeUSD, memePaidCommissionUSD, memeActivityCashbackUSD, err := t.calculateMemeFeeBreakdown(treeNodeUserIDs)
contractTotalFeeUSD, contractPaidCommissionUSD, err := t.calculateContractFeeBreakdown(treeNodeUserIDs)

// Line 213-219: Cậ<PERSON> nh<PERSON>t cả hai loại commission
memeNetFeeUSD := memeTotalFeeUSD.Sub(memeActivityCashbackUSD).Mul(commissionRateN).Sub(memePaidCommissionUSD)
contractNetFeeUSD := contractTotalFeeUSD.Mul(commissionRateN).Sub(contractPaidCommissionUSD)
```

### 2. **Cron Schedule** - Chạy mỗi giờ
```yaml
# config.yaml line 163
- id: "infinite_agent_commission"
  cron: "0 0 * * * *"  # Chạy mỗi giờ, không phải mỗi ngày!
```

### 3. **Luồng xử lý riêng biệt**
- **Meme trades**: Xử lý qua `affiliate_subscriber.go` → `ProcessAffiliateTransaction()`
- **Contract trades**: Xử lý qua `hyperliquid_transaction_task.go` → `ProcessContractCommission()`
- **Infinite commission**: Tính toán tổng hợp cả hai loại

## 🚨 Vấn đề cụ thể:

### Scenario Mike gặp phải:
1. Mike thực hiện **1 meme trade**
2. **Meme commission** được tính đúng từ NATS event
3. **Infinite commission task** chạy và tính toán lại **cả meme và contract**
4. Kết quả: **Cả hai loại commission đều hiển thị tăng**

### Tại sao cả hai đều tăng?
- Infinite commission task **không phân biệt** trade type
- Nó tính toán **tổng commission** cho cả meme và contract
- Khi có meme trade mới, cả `memeNetFeeUSD` và `contractNetFeeUSD` đều được recalculate

## 💡 Giải pháp:

### 1. **Immediate Fix** - Thêm logging để debug
```go
// Đã thêm vào hyperliquid_transaction_task.go và affiliate_service.go
global.GVA_LOG.Info("Processing trade with source identification",
    zap.String("trade_type", tradeType),
    zap.String("processing_source", "affiliate_nats_event"), // hoặc "hyperliquid_nats_event"
    zap.String("user_id", userID))
```

### 2. **Root Cause Fix** - Sửa infinite commission logic
Cần sửa logic để chỉ update commission type tương ứng với trade type:

```go
// Thay vì update cả hai, chỉ update loại tương ứng
if hasNewMemeTradeData {
    updateMemeCommission()
}
if hasNewContractTradeData {
    updateContractCommission()
}
```

### 3. **Configuration Fix** - Sửa cron schedule
```yaml
# Thay đổi từ mỗi giờ thành mỗi ngày
- id: "infinite_agent_commission"
  cron: "0 5 0 * * *"  # 00:05 mỗi ngày
```

## 🔧 Các bước debug tiếp theo:

1. **Kiểm tra logs** với pattern:
   ```bash
   grep -E "(Processing.*trade.*MEME|Processing.*trade.*PERPETUAL|calculate contract commission|infinite agent commission)" logs/
   ```

2. **Kiểm tra database** để xác nhận:
   ```sql
   -- Kiểm tra meme transactions
   SELECT COUNT(*) FROM affiliate_transactions WHERE user_id = 'mike_user_id';
   
   -- Kiểm tra contract transactions  
   SELECT COUNT(*) FROM hyper_liquid_transactions WHERE user_id = 'mike_user_id';
   
   -- Kiểm tra infinite commission updates
   SELECT * FROM infinite_agent_configs WHERE user_id IN (SELECT referrer_id FROM user_referrals WHERE user_id = 'mike_user_id');
   ```

3. **Monitor real-time** khi Mike trade:
   ```bash
   tail -f logs/app.log | grep -E "(mike_user_id|Processing.*trade)"
   ```

## 📊 Expected vs Actual Behavior:

### Expected (Đúng):
- Mike làm 1 meme trade → Chỉ meme commission tăng
- Mike làm 1 contract trade → Chỉ contract commission tăng

### Actual (Hiện tại):
- Mike làm 1 meme trade → **Cả meme và contract commission đều tăng**
- Nguyên nhân: Infinite commission task recalculate tất cả

## 🎯 Kết luận:
Vấn đề **KHÔNG** phải do NATS monitoring bị lỗi, mà do **infinite commission calculation** tính toán lại cả hai loại commission mỗi khi có trade mới, bất kể trade type.
